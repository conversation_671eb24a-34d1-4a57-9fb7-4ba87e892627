@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    视频播放器项目启动脚本
echo ========================================
echo.

REM 检查Java环境
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误：未找到Java环境，请先安装Java 17或更高版本
    pause
    exit /b 1
)

REM 检查Maven环境
mvn -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误：未找到Maven环境，请先安装Maven
    pause
    exit /b 1
)

REM 检查配置文件
if not exist ".env" (
    echo ⚠️  警告：未找到.env配置文件
    echo 📝 请复制.env.example为.env并配置您的阿里云OSS信息
    echo.
    echo 是否继续使用默认配置启动？（Y/N）
    set /p choice=
    if /i not "%choice%"=="Y" (
        echo 启动已取消
        pause
        exit /b 1
    )
)

echo 🚀 正在启动视频播放器...
echo.
echo 📋 启动信息：
echo    - 端口：5000
echo    - 本地访问：http://localhost:5000
echo    - 管理页面：http://localhost:5000/admin
echo.

REM 启动应用
mvn spring-boot:run

pause
