#!/bin/bash

echo ""
echo "========================================"
echo "    视频播放器项目启动脚本"
echo "========================================"
echo ""

# 检查Java环境
if ! command -v java &> /dev/null; then
    echo "❌ 错误：未找到Java环境，请先安装Java 17或更高版本"
    exit 1
fi

# 检查Maven环境
if ! command -v mvn &> /dev/null; then
    echo "❌ 错误：未找到Maven环境，请先安装Maven"
    exit 1
fi

# 检查配置文件
if [ ! -f ".env" ]; then
    echo "⚠️  警告：未找到.env配置文件"
    echo "📝 请复制.env.example为.env并配置您的阿里云OSS信息"
    echo ""
    read -p "是否继续使用默认配置启动？(y/N): " choice
    case "$choice" in 
        y|Y ) echo "继续启动...";;
        * ) echo "启动已取消"; exit 1;;
    esac
fi

echo "🚀 正在启动视频播放器..."
echo ""
echo "📋 启动信息："
echo "   - 端口：5000"
echo "   - 本地访问：http://localhost:5000"
echo "   - 管理页面：http://localhost:5000/admin"
echo ""

# 启动应用
mvn spring-boot:run
